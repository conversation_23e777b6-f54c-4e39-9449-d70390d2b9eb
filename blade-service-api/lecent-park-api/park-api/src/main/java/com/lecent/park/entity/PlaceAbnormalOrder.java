package com.lecent.park.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lecent.park.en.PlaceAbnormalTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.util.Date;

/**
 * 车位异常订单实体类
 *
 * <AUTHOR>
 * @since 2025-06-27 14:12:32
 */
@Data
@TableName("c_place_abnormal_order")
@EqualsAndHashCode(callSuper = true)
public class PlaceAbnormalOrder extends BaseEntity {

    @ApiModelProperty("车位ID")
    private Long placeId;

    @ApiModelProperty("停车场ID")
    private Long parklotId;

    @ApiModelProperty("停车记录id")
    private Long parkingId;

    @ApiModelProperty("异常类型")
    private PlaceAbnormalTypeEnum abnormalType;

    @ApiModelProperty("处理人")
    private Long handlerPersonId;

    @ApiModelProperty("处理人")
    private String handlerPersonName;

    @ApiModelProperty("处理时间")
    private Date handlerDate;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("原因")
    private String reason;

    @ApiModelProperty("附件")
    private String attachment;
}
