package com.leliven.csc.application.statistics.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 首页统计视图对象
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@ApiModel("首页统计数据")
public class HomeStatisticsVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("统计时间")
    private LocalDateTime statisticsTime;

    @ApiModelProperty("客诉问题类型统计")
    private ComplaintStatistics complaintTypes;

    @ApiModelProperty("客诉来源渠道分布")
    private List<SourceDistribution> complaintSources;

    @ApiModelProperty("客诉来源分布")
    private List<SourceDistribution> complaintDistribution;

    @ApiModelProperty("工单分析")
    private WorkOrderStatistics workOrderAnalysis;

    @ApiModelProperty("工单处理趋势")
    private TrendAnalysis workOrderTrend;

    @ApiModelProperty("异常订单分析")
    private ExceptionOrderAnalysis exceptionOrderAnalysis;

    /**
     * 客诉统计
     */
    @Data
    @ApiModel("客诉统计")
    public static class ComplaintStatistics implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty("客诉问题总数")
        private Integer totalCount = 9826;

        @ApiModelProperty("问题类型列表")
        private List<ComplaintTypeItem> items;

        @Data
        @ApiModel("客诉问题类型项")
        public static class ComplaintTypeItem implements Serializable {
            private static final long serialVersionUID = 1L;

            @ApiModelProperty("类型名称")
            private String name;

            @ApiModelProperty("数量")
            private Integer count;
        }
    }

    /**
     * 来源分布
     */
    @Data
    @ApiModel("来源分布")
    public static class SourceDistribution implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty("名称")
        private String name;

        @ApiModelProperty("数量")
        private Integer count;

        @ApiModelProperty("占比")
        private Double percentage;
    }

    /**
     * 工单统计
     */
    @Data
    @ApiModel("工单统计")
    public static class WorkOrderStatistics implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty("工单总数")
        private Integer totalCount = 9826;

        @ApiModelProperty("新增数")
        private Integer newCount = 368;

        @ApiModelProperty("已处理")
        private Integer processedCount = 8874;

        @ApiModelProperty("未处理")
        private Integer unprocessedCount = 1359;

        @ApiModelProperty("处理率")
        private BigDecimal processingRate = new BigDecimal("98.23");
    }

    /**
     * 趋势分析
     */
    @Data
    @ApiModel("趋势分析")
    public static class TrendAnalysis implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty("日期标签")
        private List<String> dateLabels;

        @ApiModelProperty("生产工单数据")
        private List<Integer> productionData;

        @ApiModelProperty("处理工单数据")
        private List<Integer> processedData;
    }

    /**
     * 异常订单分析
     */
    @Data
    @ApiModel("异常订单分析")
    public static class ExceptionOrderAnalysis implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty("异常总数")
        private Integer totalCount = 928531;

        @ApiModelProperty("异常分布")
        private List<ExceptionTypeItem> distribution;

        @ApiModelProperty("趋势数据")
        private TrendAnalysis trend;

        @Data
        @ApiModel("异常类型项")
        public static class ExceptionTypeItem implements Serializable {
            private static final long serialVersionUID = 1L;

            @ApiModelProperty("类型名称")
            private String name;

            @ApiModelProperty("数量")
            private Integer count;

            @ApiModelProperty("占比")
            private Double percentage;
        }
    }
} 