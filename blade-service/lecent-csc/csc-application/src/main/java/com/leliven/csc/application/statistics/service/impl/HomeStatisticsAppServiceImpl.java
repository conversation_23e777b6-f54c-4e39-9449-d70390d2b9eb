package com.leliven.csc.application.statistics.service.impl;

import java.time.LocalDate;
import java.util.Collections;

import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;

import com.leliven.csc.application.statistics.assembler.HomeStatisticsAssembler;
import com.leliven.csc.application.statistics.dto.HomeStatisticsQueryDTO;
import com.leliven.csc.application.statistics.dto.HomeStatisticsVO;
import com.leliven.csc.application.statistics.service.HomeStatisticsAppService;
import com.leliven.csc.domain.statistics.model.HomeStatistics;
import com.leliven.csc.domain.statistics.service.HomeStatisticsService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 首页统计应用服务实现
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HomeStatisticsAppServiceImpl implements HomeStatisticsAppService {

    private final HomeStatisticsService homeStatisticsService;
    private final HomeStatisticsAssembler assembler;

    @Override
    public HomeStatisticsVO getHomeStatistics(HomeStatisticsQueryDTO queryDTO) {
        log.info("获取首页统计数据，参数: {}", queryDTO);
        
        // 处理查询参数
        processQueryParams(queryDTO);
        
        // 获取统计数据
        HomeStatistics homeStatistics = homeStatisticsService.generateHomeStatistics(
                queryDTO.getTenantId(),
                queryDTO.getParkLotIds(),
                queryDTO.getStartDate(),
                queryDTO.getEndDate()
        );
        
        // 转换为VO
        return assembler.toVO(homeStatistics);
    }

    @Override
    public HomeStatisticsVO getRealtimeHomeStatistics(HomeStatisticsQueryDTO queryDTO) {
        log.info("获取实时首页统计数据，参数: {}", queryDTO);
        
        // 处理查询参数
        processQueryParams(queryDTO);
        
        // 获取实时统计数据
        HomeStatistics homeStatistics = homeStatisticsService.getRealtimeHomeStatistics(
                queryDTO.getTenantId(),
                queryDTO.getParkLotIds()
        );
        
        // 转换为VO
        return assembler.toVO(homeStatistics);
    }

    @Override
    public HomeStatisticsVO refreshHomeStatistics(HomeStatisticsQueryDTO queryDTO) {
        log.info("刷新首页统计数据，参数: {}", queryDTO);
        
        // 处理查询参数
        processQueryParams(queryDTO);
        
        // 先获取现有统计数据
        HomeStatistics homeStatistics = homeStatisticsService.generateHomeStatistics(
                queryDTO.getTenantId(),
                queryDTO.getParkLotIds(),
                queryDTO.getStartDate(),
                queryDTO.getEndDate()
        );
        
        // 刷新统计数据
        HomeStatistics refreshedStatistics = homeStatisticsService.refreshStatistics(homeStatistics);
        
        // 转换为VO
        return assembler.toVO(refreshedStatistics);
    }

    /**
     * 处理查询参数
     *
     * @param queryDTO 查询参数
     */
    private void processQueryParams(HomeStatisticsQueryDTO queryDTO) {
        // 设置租户ID
        if (Func.isBlank(queryDTO.getTenantId())) {
            queryDTO.setTenantId(AuthUtil.getTenantId());
        }

        // 处理日期参数
        processDateParams(queryDTO);

        // 处理车场ID参数
        if (Func.isEmpty(queryDTO.getParkLotIds())) {
            queryDTO.setParkLotIds(Collections.emptyList());
        }
    }

    /**
     * 处理日期参数
     *
     * @param queryDTO 查询参数
     */
    private void processDateParams(HomeStatisticsQueryDTO queryDTO) {
        Integer periodType = queryDTO.getPeriodType();
        LocalDate now = LocalDate.now();

        if (periodType == null) {
            periodType = 1; // 默认今日
        }

        switch (periodType) {
            case 1: // 今日
                queryDTO.setStartDate(now);
                queryDTO.setEndDate(now);
                break;
            case 2: // 本周
                queryDTO.setStartDate(now.minusDays(now.getDayOfWeek().getValue() - 1));
                queryDTO.setEndDate(now);
                break;
            case 3: // 本月
                queryDTO.setStartDate(now.withDayOfMonth(1));
                queryDTO.setEndDate(now);
                break;
            case 4: // 自定义
                if (queryDTO.getStartDate() == null) {
                    queryDTO.setStartDate(now.minusDays(7));
                }
                if (queryDTO.getEndDate() == null) {
                    queryDTO.setEndDate(now);
                }
                break;
            default:
                queryDTO.setStartDate(now);
                queryDTO.setEndDate(now);
        }
    }
} 