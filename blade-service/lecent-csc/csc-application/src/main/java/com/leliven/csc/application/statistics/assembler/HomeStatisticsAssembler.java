package com.leliven.csc.application.statistics.assembler;

import com.leliven.csc.application.statistics.dto.HomeStatisticsVO;
import com.leliven.csc.domain.statistics.model.HomeStatistics;
import com.leliven.csc.domain.statistics.model.valueobject.ComplaintSource;
import com.leliven.csc.domain.statistics.model.valueobject.ComplaintType;
import com.leliven.csc.domain.statistics.model.valueobject.TrendData;
import com.leliven.csc.domain.statistics.model.valueobject.WorkOrderAnalysis;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 首页统计组装器
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Component
public class HomeStatisticsAssembler {

    /**
     * 将领域模型转换为VO
     *
     * @param homeStatistics 领域模型
     * @return VO对象
     */
    public HomeStatisticsVO toVO(HomeStatistics homeStatistics) {
        if (homeStatistics == null) {
            return createDefaultVO();
        }

        HomeStatisticsVO vo = new HomeStatisticsVO();
        vo.setStatisticsTime(homeStatistics.getStatisticsTime());

        // 转换客诉问题类型统计
        vo.setComplaintTypes(convertComplaintTypes(homeStatistics.getComplaintTypes()));

        // 转换客诉来源渠道分布
        vo.setComplaintSources(convertComplaintSources(homeStatistics.getComplaintSources()));
        vo.setComplaintDistribution(convertComplaintSources(homeStatistics.getComplaintSources()));

        // 转换工单分析
        vo.setWorkOrderAnalysis(convertWorkOrderAnalysis(homeStatistics.getWorkOrderAnalysis()));

        // 转换工单处理趋势
        vo.setWorkOrderTrend(convertWorkOrderTrend(homeStatistics.getWorkOrderTrend()));

        // 转换异常订单分析
        vo.setExceptionOrderAnalysis(convertExceptionOrderAnalysis(homeStatistics.getAbnormalOrderAnalysis()));

        return vo;
    }

    /**
     * 转换客诉问题类型统计
     */
    private HomeStatisticsVO.ComplaintStatistics convertComplaintTypes(List<ComplaintType> complaintTypes) {
        HomeStatisticsVO.ComplaintStatistics statistics = new HomeStatisticsVO.ComplaintStatistics();
        
        if (Func.isEmpty(complaintTypes)) {
            statistics.setItems(createDefaultComplaintTypes());
            return statistics;
        }

        Integer totalCount = complaintTypes.stream().mapToInt(ComplaintType::getCount).sum();
        statistics.setTotalCount(totalCount);

        List<HomeStatisticsVO.ComplaintStatistics.ComplaintTypeItem> items = complaintTypes.stream()
                .map(this::convertComplaintTypeItem)
                .collect(Collectors.toList());
        statistics.setItems(items);

        return statistics;
    }

    /**
     * 转换客诉问题类型项
     */
    private HomeStatisticsVO.ComplaintStatistics.ComplaintTypeItem convertComplaintTypeItem(ComplaintType complaintType) {
        HomeStatisticsVO.ComplaintStatistics.ComplaintTypeItem item = 
                new HomeStatisticsVO.ComplaintStatistics.ComplaintTypeItem();
        item.setName(complaintType.getName());
        item.setCount(complaintType.getCount());
        return item;
    }

    /**
     * 转换客诉来源分布
     */
    private List<HomeStatisticsVO.SourceDistribution> convertComplaintSources(List<ComplaintSource> complaintSources) {
        if (Func.isEmpty(complaintSources)) {
            return createDefaultComplaintSources();
        }

        return complaintSources.stream()
                .map(this::convertComplaintSource)
                .collect(Collectors.toList());
    }

    /**
     * 转换客诉来源项
     */
    private HomeStatisticsVO.SourceDistribution convertComplaintSource(ComplaintSource source) {
        HomeStatisticsVO.SourceDistribution distribution = new HomeStatisticsVO.SourceDistribution();
        distribution.setName(source.getName());
        distribution.setCount(source.getCount());
        distribution.setPercentage(source.getPercentage());
        return distribution;
    }

    /**
     * 转换工单分析
     */
    private HomeStatisticsVO.WorkOrderStatistics convertWorkOrderAnalysis(WorkOrderAnalysis workOrderAnalysis) {
        HomeStatisticsVO.WorkOrderStatistics statistics = new HomeStatisticsVO.WorkOrderStatistics();
        
        if (workOrderAnalysis == null) {
            return statistics; // 返回默认值
        }

        statistics.setTotalCount(workOrderAnalysis.getTotalCount());
        statistics.setNewCount(workOrderAnalysis.getNewCount());
        statistics.setProcessedCount(workOrderAnalysis.getProcessedCount());
        statistics.setUnprocessedCount(workOrderAnalysis.getUnprocessedCount());
        statistics.setProcessingRate(workOrderAnalysis.getProcessingRate());

        return statistics;
    }

    /**
     * 转换工单处理趋势
     */
    private HomeStatisticsVO.TrendAnalysis convertWorkOrderTrend(List<TrendData> trendDataList) {
        HomeStatisticsVO.TrendAnalysis trendAnalysis = new HomeStatisticsVO.TrendAnalysis();
        
        if (Func.isEmpty(trendDataList)) {
            trendAnalysis.setDateLabels(createDefaultDateLabels());
            trendAnalysis.setProductionData(createDefaultTrendData());
            trendAnalysis.setProcessedData(createDefaultTrendData());
            return trendAnalysis;
        }

        List<String> dateLabels = trendDataList.stream()
                .map(TrendData::getDateLabel)
                .collect(Collectors.toList());
        
        List<Integer> productionData = trendDataList.stream()
                .map(TrendData::getProductionWorkOrders)
                .collect(Collectors.toList());
        
        List<Integer> processedData = trendDataList.stream()
                .map(TrendData::getProcessedWorkOrders)
                .collect(Collectors.toList());

        trendAnalysis.setDateLabels(dateLabels);
        trendAnalysis.setProductionData(productionData);
        trendAnalysis.setProcessedData(processedData);

        return trendAnalysis;
    }

    /**
     * 转换异常订单分析
     */
    private HomeStatisticsVO.ExceptionOrderAnalysis convertExceptionOrderAnalysis(List<ComplaintType> abnormalOrderList) {
        HomeStatisticsVO.ExceptionOrderAnalysis analysis = new HomeStatisticsVO.ExceptionOrderAnalysis();
        
        if (Func.isEmpty(abnormalOrderList)) {
            analysis.setDistribution(createDefaultExceptionDistribution());
            analysis.setTrend(createDefaultTrend());
            return analysis;
        }

        Integer totalCount = abnormalOrderList.stream().mapToInt(ComplaintType::getCount).sum();
        analysis.setTotalCount(totalCount);

        List<HomeStatisticsVO.ExceptionOrderAnalysis.ExceptionTypeItem> distribution = 
                abnormalOrderList.stream()
                        .map(this::convertExceptionTypeItem)
                        .collect(Collectors.toList());
        analysis.setDistribution(distribution);

        // 创建默认趋势数据
        analysis.setTrend(createDefaultTrend());

        return analysis;
    }

    /**
     * 转换异常类型项
     */
    private HomeStatisticsVO.ExceptionOrderAnalysis.ExceptionTypeItem convertExceptionTypeItem(ComplaintType complaintType) {
        HomeStatisticsVO.ExceptionOrderAnalysis.ExceptionTypeItem item = 
                new HomeStatisticsVO.ExceptionOrderAnalysis.ExceptionTypeItem();
        item.setName(complaintType.getName());
        item.setCount(complaintType.getCount());
        item.setPercentage(complaintType.getPercentage());
        return item;
    }

    /**
     * 创建默认VO
     */
    private HomeStatisticsVO createDefaultVO() {
        HomeStatisticsVO vo = new HomeStatisticsVO();
        vo.setComplaintTypes(new HomeStatisticsVO.ComplaintStatistics());
        vo.getComplaintTypes().setItems(createDefaultComplaintTypes());
        vo.setComplaintSources(createDefaultComplaintSources());
        vo.setComplaintDistribution(createDefaultComplaintSources());
        vo.setWorkOrderAnalysis(new HomeStatisticsVO.WorkOrderStatistics());
        vo.setWorkOrderTrend(createDefaultTrend());
        vo.setExceptionOrderAnalysis(new HomeStatisticsVO.ExceptionOrderAnalysis());
        vo.getExceptionOrderAnalysis().setDistribution(createDefaultExceptionDistribution());
        vo.getExceptionOrderAnalysis().setTrend(createDefaultTrend());
        return vo;
    }

    /**
     * 创建默认客诉问题类型
     */
    private List<HomeStatisticsVO.ComplaintStatistics.ComplaintTypeItem> createDefaultComplaintTypes() {
        List<HomeStatisticsVO.ComplaintStatistics.ComplaintTypeItem> items = new ArrayList<>();
        
        String[] names = {"城际问题", "订单异常", "人工操作问题", "支付问题", "微信无感支付", "开票通信", "月卡问题", "自卡设备故障", "车牌识别异常", "其他"};
        Integer[] counts = {1257, 1135, 1106, 963, 927, 905, 871, 837, 751, 634};
        
        for (int i = 0; i < names.length; i++) {
            HomeStatisticsVO.ComplaintStatistics.ComplaintTypeItem item = 
                    new HomeStatisticsVO.ComplaintStatistics.ComplaintTypeItem();
            item.setName(names[i]);
            item.setCount(counts[i]);
            items.add(item);
        }
        
        return items;
    }

    /**
     * 创建默认客诉来源
     */
    private List<HomeStatisticsVO.SourceDistribution> createDefaultComplaintSources() {
        List<HomeStatisticsVO.SourceDistribution> sources = new ArrayList<>();
        
        String[] names = {"400客服热线", "微信群", "12345", "微信小程序"};
        Integer[] counts = {550, 280, 115, 115};
        Double[] percentages = {57.23, 30.43, 12.36, 12.36};
        
        for (int i = 0; i < names.length; i++) {
            HomeStatisticsVO.SourceDistribution source = new HomeStatisticsVO.SourceDistribution();
            source.setName(names[i]);
            source.setCount(counts[i]);
            source.setPercentage(percentages[i]);
            sources.add(source);
        }
        
        return sources;
    }

    /**
     * 创建默认异常分布
     */
    private List<HomeStatisticsVO.ExceptionOrderAnalysis.ExceptionTypeItem> createDefaultExceptionDistribution() {
        List<HomeStatisticsVO.ExceptionOrderAnalysis.ExceptionTypeItem> items = new ArrayList<>();
        
        String[] names = {"普通车", "孔在县", "贵定县", "南明区", "开阳县"};
        Integer[] counts = {34201, 9285, 4257, 9285, 4257};
        Double[] percentages = {57.23, 30.43, 12.36, 30.43, 12.36};
        
        for (int i = 0; i < names.length; i++) {
            HomeStatisticsVO.ExceptionOrderAnalysis.ExceptionTypeItem item = 
                    new HomeStatisticsVO.ExceptionOrderAnalysis.ExceptionTypeItem();
            item.setName(names[i]);
            item.setCount(counts[i]);
            item.setPercentage(percentages[i]);
            items.add(item);
        }
        
        return items;
    }

    /**
     * 创建默认趋势数据
     */
    private HomeStatisticsVO.TrendAnalysis createDefaultTrend() {
        HomeStatisticsVO.TrendAnalysis trend = new HomeStatisticsVO.TrendAnalysis();
        trend.setDateLabels(createDefaultDateLabels());
        trend.setProductionData(createDefaultTrendData());
        trend.setProcessedData(createDefaultTrendData());
        return trend;
    }

    /**
     * 创建默认日期标签
     */
    private List<String> createDefaultDateLabels() {
        return Arrays.asList("05-01", "05-02", "05-03", "05-04", "05-05", "05-06", "05-07", 
                            "05-08", "05-09", "05-10", "05-11", "05-12", "05-13", "05-14", "05-15");
    }

    /**
     * 创建默认趋势数据
     */
    private List<Integer> createDefaultTrendData() {
        return Arrays.asList(200, 300, 100, 50, 350, 240, 120, 220, 180, 160, 280, 320, 200, 150, 100);
    }
} 