package com.leliven.csc.application.statistics.service;

import com.leliven.csc.application.statistics.dto.HomeStatisticsQueryDTO;
import com.leliven.csc.application.statistics.dto.HomeStatisticsVO;

/**
 * 首页统计应用服务接口
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public interface HomeStatisticsAppService {

    /**
     * 获取首页统计数据
     *
     * @param queryDTO 查询参数
     * @return 首页统计数据
     */
    HomeStatisticsVO getHomeStatistics(HomeStatisticsQueryDTO queryDTO);

    /**
     * 获取实时首页统计数据
     *
     * @param queryDTO 查询参数
     * @return 实时首页统计数据
     */
    HomeStatisticsVO getRealtimeHomeStatistics(HomeStatisticsQueryDTO queryDTO);

    /**
     * 刷新首页统计数据
     *
     * @param queryDTO 查询参数
     * @return 刷新后的首页统计数据
     */
    HomeStatisticsVO refreshHomeStatistics(HomeStatisticsQueryDTO queryDTO);
} 