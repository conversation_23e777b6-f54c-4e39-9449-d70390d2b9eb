package com.leliven.csc.application.statistics.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 首页统计查询DTO
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@ApiModel("首页统计查询参数")
public class HomeStatisticsQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("租户ID")
    private String tenantId;

    @ApiModelProperty("车场ID列表")
    private List<Long> parkLotIds;

    @ApiModelProperty("开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty("结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty("统计周期类型：1-今日，2-本周，3-本月，4-自定义")
    private Integer periodType;

    @ApiModelProperty("是否包含趋势数据")
    private Boolean includeTrend = true;

    @ApiModelProperty("是否实时查询")
    private Boolean realtime = false;
} 