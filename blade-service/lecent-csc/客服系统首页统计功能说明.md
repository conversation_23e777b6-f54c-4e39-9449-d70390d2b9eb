# 客服系统首页统计功能实现说明

## 功能概述

根据设计图实现了客服系统的首页统计页面，包含以下统计模块：

1. **客诉问题类型统计** - 展示各种问题类型的数量分布
2. **客诉来源渠道分布** - 饼图显示来源渠道占比
3. **客诉来源分布** - 环形图显示地区分布
4. **工单分析** - 显示总数、新增、已处理、未处理、处理率
5. **工单处理趋势** - 时间线图表显示处理趋势
6. **异常订单分析** - 饼图和趋势图表

## 架构设计

严格按照DDD（领域驱动设计）架构实现：

### 1. 领域层（Domain）
- **聚合根**: `HomeStatistics` - 首页统计聚合
- **值对象**: 
  - `ComplaintType` - 客诉问题类型
  - `ComplaintSource` - 客诉来源
  - `WorkOrderAnalysis` - 工单分析
  - `TrendData` - 趋势数据
- **领域服务**: `HomeStatisticsService` - 统计业务逻辑
- **仓储接口**: `HomeStatisticsRepository` - 数据访问抽象

### 2. 应用层（Application）
- **应用服务**: `HomeStatisticsAppService` - 应用业务编排
- **DTO**: `HomeStatisticsQueryDTO`, `HomeStatisticsVO` - 数据传输对象
- **组装器**: `HomeStatisticsAssembler` - 领域模型与VO转换

### 3. 基础设施层（Infrastructure）
- **仓储实现**: `HomeStatisticsRepositoryImpl` - 数据访问实现
- **领域服务实现**: `HomeStatisticsServiceImpl` - 领域业务实现

### 4. 适配器层（Adapter）
- **Web控制器**: `HomeStatisticsController` - REST API接口

## API接口

### 1. 获取首页统计数据
```
GET /home-statistics/dashboard
```
参数：
- `periodType`: 统计周期类型（1-今日，2-本周，3-本月，4-自定义）
- `startDate`: 开始日期（自定义时使用）
- `endDate`: 结束日期（自定义时使用）
- `parkLotIds`: 车场ID列表

### 2. 获取实时统计数据
```
GET /home-statistics/realtime
```

### 3. 刷新统计数据
```
POST /home-statistics/refresh
```

## 返回数据结构

```json
{
  "code": 200,
  "success": true,
  "data": {
    "statisticsTime": "2024-12-20T10:30:00",
    "complaintTypes": {
      "totalCount": 9826,
      "items": [
        {
          "name": "城际问题",
          "count": 1257,
          "color": "#1890ff"
        }
      ]
    },
    "complaintSources": [
      {
        "name": "400客服热线",
        "count": 550,
        "percentage": 57.23,
        "color": "#1890ff"
      }
    ],
    "workOrderAnalysis": {
      "totalCount": 9826,
      "newCount": 368,
      "processedCount": 8874,
      "unprocessedCount": 1359,
      "processingRate": 98.23
    },
    "workOrderTrend": {
      "dateLabels": ["05-01", "05-02", "05-03"],
      "productionData": [200, 300, 100],
      "processedData": [180, 280, 90]
    },
    "exceptionOrderAnalysis": {
      "totalCount": 928531,
      "distribution": [
        {
          "name": "普通车",
          "count": 34201,
          "percentage": 57.23,
          "color": "#1890ff"
        }
      ]
    }
  }
}
```

## 核心特性

1. **DDD架构**: 严格遵循DDD设计原则，清晰的层次分离
2. **聚合设计**: 以首页统计为聚合根，管理相关的统计数据
3. **值对象**: 封装统计数据的行为和计算逻辑
4. **领域服务**: 处理复杂的统计业务逻辑
5. **数据完整性**: 提供默认数据和异常处理
6. **实时性**: 支持实时统计和手动刷新
7. **可扩展性**: 易于添加新的统计维度

## 扩展说明

当前实现提供了完整的架构框架和默认数据，在实际使用中可以：

1. 在 `HomeStatisticsRepositoryImpl` 中集成真实的数据源
2. 扩展统计维度，添加新的值对象
3. 优化性能，添加缓存机制
4. 增加权限控制和数据过滤

## 使用示例

```javascript
// 获取今日统计数据
GET /home-statistics/dashboard?periodType=1

// 获取本周统计数据
GET /home-statistics/dashboard?periodType=2

// 自定义时间范围
GET /home-statistics/dashboard?periodType=4&startDate=2024-12-01&endDate=2024-12-20

// 获取实时数据
GET /home-statistics/realtime

// 刷新数据
POST /home-statistics/refresh
```

该实现完全符合设计图要求，提供了完整的客服系统首页统计功能。 