package com.leliven.csc.adapter.statistics.web;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.leliven.csc.application.statistics.dto.HomeStatisticsQueryDTO;
import com.leliven.csc.application.statistics.dto.HomeStatisticsVO;
import com.leliven.csc.application.statistics.service.HomeStatisticsAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

/**
 * 首页统计控制器
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/home-statistics")
@Api(value = "首页统计", tags = "首页统计接口")
public class HomeStatisticsController extends BladeController {

    private final HomeStatisticsAppService homeStatisticsAppService;

    @GetMapping("/dashboard")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "获取首页统计数据", notes = "获取客服系统首页统计仪表板数据")
    public R<HomeStatisticsVO> getHomeStatistics(HomeStatisticsQueryDTO queryDTO) {
        log.info("获取首页统计数据，参数: {}", queryDTO);
        HomeStatisticsVO result = homeStatisticsAppService.getHomeStatistics(queryDTO);
        return R.data(result);
    }

    @GetMapping("/realtime")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "获取实时首页统计数据", notes = "获取实时的首页统计数据")
    public R<HomeStatisticsVO> getRealtimeHomeStatistics(HomeStatisticsQueryDTO queryDTO) {
        log.info("获取实时首页统计数据，参数: {}", queryDTO);
        HomeStatisticsVO result = homeStatisticsAppService.getRealtimeHomeStatistics(queryDTO);
        return R.data(result);
    }

    @PostMapping("/refresh")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "刷新首页统计数据", notes = "手动刷新首页统计数据")
    public R<HomeStatisticsVO> refreshHomeStatistics(@RequestBody HomeStatisticsQueryDTO queryDTO) {
        log.info("刷新首页统计数据，参数: {}", queryDTO);
        HomeStatisticsVO result = homeStatisticsAppService.refreshHomeStatistics(queryDTO);
        return R.data(result);
    }
} 