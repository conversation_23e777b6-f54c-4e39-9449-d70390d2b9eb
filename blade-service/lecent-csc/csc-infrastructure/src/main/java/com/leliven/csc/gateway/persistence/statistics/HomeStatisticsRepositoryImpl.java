package com.leliven.csc.gateway.persistence.statistics;

import com.leliven.csc.domain.statistics.model.valueobject.*;
import com.leliven.csc.domain.statistics.repository.HomeStatisticsRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 首页统计仓储实现（简化版本）
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@Repository
public class HomeStatisticsRepositoryImpl implements HomeStatisticsRepository {

    @Override
    public List<ComplaintType> getComplaintTypeStatistics(String tenantId, List<Long> parkLotIds, LocalDate startDate, LocalDate endDate) {
        log.info("获取客诉问题类型统计，租户: {}, 车场: {}, 时间范围: {} - {}", tenantId, parkLotIds, startDate, endDate);
        return createDefaultComplaintTypes();
    }

    @Override
    public List<ComplaintSource> getComplaintSourceStatistics(String tenantId, List<Long> parkLotIds, LocalDate startDate, LocalDate endDate) {
        log.info("获取客诉来源渠道分布，租户: {}, 车场: {}, 时间范围: {} - {}", tenantId, parkLotIds, startDate, endDate);
        return createDefaultComplaintSources();
    }

    @Override
    public WorkOrderAnalysis getWorkOrderAnalysis(String tenantId, List<Long> parkLotIds, LocalDate startDate, LocalDate endDate) {
        log.info("获取工单分析数据，租户: {}, 车场: {}, 时间范围: {} - {}", tenantId, parkLotIds, startDate, endDate);
        return WorkOrderAnalysis.of(9826, 368, 8874, 1359);
    }

    @Override
    public List<TrendData> getWorkOrderTrendData(String tenantId, List<Long> parkLotIds, LocalDate startDate, LocalDate endDate) {
        log.info("获取工单处理趋势数据，租户: {}, 车场: {}, 时间范围: {} - {}", tenantId, parkLotIds, startDate, endDate);
        return createDefaultTrendData(startDate, endDate);
    }

    @Override
    public List<ComplaintType> getAbnormalOrderAnalysis(String tenantId, List<Long> parkLotIds, LocalDate startDate, LocalDate endDate) {
        log.info("获取异常订单分析数据，租户: {}, 车场: {}, 时间范围: {} - {}", tenantId, parkLotIds, startDate, endDate);
        return createDefaultAbnormalOrderAnalysis();
    }

    /**
     * 创建默认客诉问题类型
     */
    private List<ComplaintType> createDefaultComplaintTypes() {
        List<ComplaintType> types = new ArrayList<>();
        types.add(ComplaintType.of("1", "城际问题", 1257));
        types.add(ComplaintType.of("2", "订单异常", 1135));
        types.add(ComplaintType.of("3", "人工操作问题", 1106));
        types.add(ComplaintType.of("4", "支付问题", 963));
        types.add(ComplaintType.of("5", "微信无感支付", 927));
        types.add(ComplaintType.of("6", "开票通信", 905));
        types.add(ComplaintType.of("7", "月卡问题", 871));
        types.add(ComplaintType.of("8", "自卡设备故障", 837));
        types.add(ComplaintType.of("9", "车牌识别异常", 751));
        types.add(ComplaintType.of("10", "其他", 634));
        return types;
    }

    /**
     * 创建默认客诉来源
     */
    private List<ComplaintSource> createDefaultComplaintSources() {
        List<ComplaintSource> sources = new ArrayList<>();
        sources.add(ComplaintSource.of("1", "400客服热线", 550, 57.23));
        sources.add(ComplaintSource.of("2", "微信群", 280, 30.43));
        sources.add(ComplaintSource.of("3", "12345", 115, 12.36));
        sources.add(ComplaintSource.of("4", "微信小程序", 115, 12.36));
        return sources;
    }

    /**
     * 创建默认趋势数据
     */
    private List<TrendData> createDefaultTrendData(LocalDate startDate, LocalDate endDate) {
        List<TrendData> trendDataList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd");
        
        // 生成日期范围内的趋势数据
        LocalDate current = startDate;
        int[] productionData = {200, 300, 100, 50, 350, 240, 120, 220, 180, 160, 280, 320, 200, 150, 100};
        int[] processedData = {180, 280, 90, 45, 320, 220, 110, 200, 160, 150, 260, 300, 180, 140, 95};
        
        int index = 0;
        while (!current.isAfter(endDate) && index < productionData.length) {
            String dateLabel = current.format(formatter);
            TrendData trendData = TrendData.of(
                    current,
                    dateLabel,
                    productionData[index % productionData.length],
                    processedData[index % processedData.length]
            );
            trendDataList.add(trendData);
            current = current.plusDays(1);
            index++;
        }
        
        return trendDataList;
    }

    /**
     * 创建默认异常订单分析
     */
    private List<ComplaintType> createDefaultAbnormalOrderAnalysis() {
        List<ComplaintType> types = new ArrayList<>();
        types.add(ComplaintType.of("1", "普通车", 34201, 57.23));
        types.add(ComplaintType.of("2", "孔在县", 9285, 30.43));
        types.add(ComplaintType.of("3", "贵定县", 4257, 12.36));
        types.add(ComplaintType.of("4", "南明区", 9285, 30.43));
        types.add(ComplaintType.of("5", "开阳县", 4257, 12.36));
        return types;
    }
} 