package com.leliven.csc.gateway.persistence.customer.mysql.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2024/10/22 10:48
 */
@Data
@TableName("d_problem_type")
@EqualsAndHashCode(callSuper = true)
public class ProblemTypeDO extends BaseEntity {
	@ApiModelProperty(value = "分类编号")
	private String code;

	@ApiModelProperty(value = "名称")
	private String name;

	@ApiModelProperty(value = "上级ID")
	private Long parentId;
}
