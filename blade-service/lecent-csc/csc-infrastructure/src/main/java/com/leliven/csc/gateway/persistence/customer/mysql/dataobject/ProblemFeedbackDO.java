package com.leliven.csc.gateway.persistence.customer.mysql.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2024/10/22 10:53
 */
@Data
@TableName("d_problem_feedback")
@EqualsAndHashCode(callSuper = true)
public class ProblemFeedbackDO extends TenantEntity {



	@ApiModelProperty(value = "车场ID")
	private Long parkLotId;

	@ApiModelProperty(value = "车场名称")
	private String parkLotName;

	@ApiModelProperty(value = "车位ID")
	private Long placeId;

	@ApiModelProperty(value = "车位编号")
	private String placeCode;

	@ApiModelProperty(value = "车牌号")
	private String plate;

	@ApiModelProperty(value = "手机号")
	private String phone;

	@ApiModelProperty(value = "发起人ID")
	private Long sponsorId;

	@ApiModelProperty(value = "发起人名称")
	private String sponsorName;

	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@JsonFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@ApiModelProperty(value = "发起时间")
	private Date sponsorTime;

	@ApiModelProperty(value = "问题来源")
	private String source;

	@ApiModelProperty(value = "问题类型")
	private String issueType;

	@ApiModelProperty(value = "问题二级类型")
	private String issueSecondType;

	@ApiModelProperty(value = "问题描述")
	private String issueDescription;

	@ApiModelProperty(value = "解决方案")
	private String resolutionPlan;

	@ApiModelProperty(value = "是否需要退款 1需要")
	private Integer isRefund;

	@ApiModelProperty(value = "退款金额")
	private BigDecimal refundAmount;

	@ApiModelProperty(value = "处理人ID")
	private Long handlerId;

	@ApiModelProperty(value = "处理人名称")
	private String handlerName;
	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@JsonFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@ApiModelProperty(value = "处理时间")
	private Date handlerTime;

	@ApiModelProperty(value = "工单编号")
	private String workOrderCode;
}
