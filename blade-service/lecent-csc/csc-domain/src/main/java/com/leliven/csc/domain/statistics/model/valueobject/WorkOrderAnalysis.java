package com.leliven.csc.domain.statistics.model.valueobject;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 工单分析值对象
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class WorkOrderAnalysis implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 工单总数
     */
    private Integer totalCount;

    /**
     * 新增数量
     */
    private Integer newCount;

    /**
     * 已处理数量
     */
    private Integer processedCount;

    /**
     * 未处理数量
     */
    private Integer unprocessedCount;

    /**
     * 处理率
     */
    private BigDecimal processingRate;

    public static WorkOrderAnalysis of(Integer totalCount, Integer newCount, Integer processedCount, Integer unprocessedCount) {
        WorkOrderAnalysis analysis = new WorkOrderAnalysis();
        analysis.totalCount = totalCount;
        analysis.newCount = newCount;
        analysis.processedCount = processedCount;
        analysis.unprocessedCount = unprocessedCount;
        analysis.calculateProcessingRate();
        return analysis;
    }

    /**
     * 计算处理率
     */
    private void calculateProcessingRate() {
        if (totalCount == null || totalCount == 0) {
            this.processingRate = BigDecimal.ZERO;
        } else {
            this.processingRate = BigDecimal.valueOf(processedCount)
                    .divide(BigDecimal.valueOf(totalCount), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
        }
    }

    /**
     * 获取处理率百分比字符串
     *
     * @return 处理率百分比
     */
    public String getProcessingRatePercentage() {
        return processingRate.setScale(2, RoundingMode.HALF_UP) + "%";
    }

    /**
     * 验证数据一致性
     *
     * @return 是否一致
     */
    public boolean isDataConsistent() {
        return totalCount.equals(processedCount + unprocessedCount);
    }
} 