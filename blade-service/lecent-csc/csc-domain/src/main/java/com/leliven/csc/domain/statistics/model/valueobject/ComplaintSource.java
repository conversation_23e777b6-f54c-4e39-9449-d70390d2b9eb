package com.leliven.csc.domain.statistics.model.valueobject;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 客诉来源值对象
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class ComplaintSource implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 来源编码
     */
    private String code;

    /**
     * 来源名称
     */
    private String name;

    /**
     * 数量
     */
    private Integer count;

    /**
     * 占比
     */
    private Double percentage;

    public static ComplaintSource of(String code, String name, Integer count) {
        return new ComplaintSource(code, name, count, 0.0);
    }

    public static ComplaintSource of(String code, String name, Integer count, Double percentage) {
        return new ComplaintSource(code, name, count, percentage);
    }

    /**
     * 计算占比
     *
     * @param total 总数
     * @return 当前对象
     */
    public ComplaintSource calculatePercentage(Integer total) {
        if (total == null || total == 0) {
            this.percentage = 0.0;
        } else {
            this.percentage = (double) this.count / total * 100;
        }
        return this;
    }

    /**
     * 设置数量
     *
     * @param count 数量
     * @return 当前对象
     */
    public ComplaintSource withCount(Integer count) {
        this.count = count;
        return this;
    }
} 