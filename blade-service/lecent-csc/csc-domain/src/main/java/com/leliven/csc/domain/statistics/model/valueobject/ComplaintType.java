package com.leliven.csc.domain.statistics.model.valueobject;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 客诉问题类型值对象
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class ComplaintType implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 类型编码
     */
    private String code;

    /**
     * 类型名称
     */
    private String name;

    /**
     * 数量
     */
    private Integer count;

    /**
     * 占比
     */
    private Double percentage;

    public static ComplaintType of(String code, String name, Integer count) {
        return new ComplaintType(code, name, count, 0.0);
    }

    public static ComplaintType of(String code, String name, Integer count, Double percentage) {
        return new ComplaintType(code, name, count, percentage);
    }

    /**
     * 计算占比
     *
     * @param total 总数
     * @return 当前对象
     */
    public ComplaintType calculatePercentage(Integer total) {
        if (total == null || total == 0) {
            this.percentage = 0.0;
        } else {
            this.percentage = (double) this.count / total * 100;
        }
        return this;
    }

    /**
     * 设置数量
     *
     * @param count 数量
     * @return 当前对象
     */
    public ComplaintType withCount(Integer count) {
        this.count = count;
        return this;
    }
} 