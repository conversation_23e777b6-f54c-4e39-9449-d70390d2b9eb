package com.leliven.csc.domain.statistics.model;

import com.leliven.csc.domain.statistics.model.valueobject.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springblade.core.tool.utils.Func;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 首页统计聚合根
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class HomeStatistics implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 统计ID
     */
    private String id;

    /**
     * 统计时间
     */
    private LocalDateTime statisticsTime;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 车场ID列表
     */
    private List<Long> parkLotIds;

    /**
     * 客诉问题类型统计
     */
    private List<ComplaintType> complaintTypes;

    /**
     * 客诉来源渠道分布
     */
    private List<ComplaintSource> complaintSources;

    /**
     * 工单分析
     */
    private WorkOrderAnalysis workOrderAnalysis;

    /**
     * 工单处理趋势
     */
    private List<TrendData> workOrderTrend;

    /**
     * 异常订单分析
     */
    private List<ComplaintType> abnormalOrderAnalysis;

    public static HomeStatistics create(String tenantId, List<Long> parkLotIds) {
        HomeStatistics statistics = new HomeStatistics();
        statistics.id = generateId();
        statistics.statisticsTime = LocalDateTime.now();
        statistics.tenantId = tenantId;
        statistics.parkLotIds = parkLotIds;
        return statistics;
    }

    /**
     * 设置客诉问题类型统计
     *
     * @param complaintTypes 客诉问题类型列表
     * @return 当前对象
     */
    public HomeStatistics withComplaintTypes(List<ComplaintType> complaintTypes) {
        this.complaintTypes = complaintTypes;
        return this;
    }

    /**
     * 设置客诉来源渠道分布
     *
     * @param complaintSources 客诉来源列表
     * @return 当前对象
     */
    public HomeStatistics withComplaintSources(List<ComplaintSource> complaintSources) {
        this.complaintSources = complaintSources;
        return this;
    }

    /**
     * 设置工单分析
     *
     * @param workOrderAnalysis 工单分析
     * @return 当前对象
     */
    public HomeStatistics withWorkOrderAnalysis(WorkOrderAnalysis workOrderAnalysis) {
        this.workOrderAnalysis = workOrderAnalysis;
        return this;
    }

    /**
     * 设置工单处理趋势
     *
     * @param workOrderTrend 工单趋势数据
     * @return 当前对象
     */
    public HomeStatistics withWorkOrderTrend(List<TrendData> workOrderTrend) {
        this.workOrderTrend = workOrderTrend;
        return this;
    }

    /**
     * 设置异常订单分析
     *
     * @param abnormalOrderAnalysis 异常订单分析
     * @return 当前对象
     */
    public HomeStatistics withAbnormalOrderAnalysis(List<ComplaintType> abnormalOrderAnalysis) {
        this.abnormalOrderAnalysis = abnormalOrderAnalysis;
        return this;
    }

    /**
     * 获取客诉问题总数
     *
     * @return 总数
     */
    public Integer getTotalComplaintCount() {
        if (Func.isEmpty(complaintTypes)) {
            return 0;
        }
        return complaintTypes.stream()
                .mapToInt(ComplaintType::getCount)
                .sum();
    }

    /**
     * 获取客诉来源总数
     *
     * @return 总数
     */
    public Integer getTotalComplaintSourceCount() {
        if (Func.isEmpty(complaintSources)) {
            return 0;
        }
        return complaintSources.stream()
                .mapToInt(ComplaintSource::getCount)
                .sum();
    }

    /**
     * 计算客诉问题类型占比
     */
    public void calculateComplaintTypePercentages() {
        if (Func.isEmpty(complaintTypes)) {
            return;
        }
        Integer total = getTotalComplaintCount();
        complaintTypes.forEach(type -> type.calculatePercentage(total));
    }

    /**
     * 计算客诉来源占比
     */
    public void calculateComplaintSourcePercentages() {
        if (Func.isEmpty(complaintSources)) {
            return;
        }
        Integer total = getTotalComplaintSourceCount();
        complaintSources.forEach(source -> source.calculatePercentage(total));
    }

    /**
     * 验证统计数据完整性
     *
     * @return 是否完整
     */
    public boolean isComplete() {
        return workOrderAnalysis != null 
                && Func.isNotEmpty(complaintTypes)
                && Func.isNotEmpty(complaintSources)
                && Func.isNotEmpty(workOrderTrend);
    }

    /**
     * 生成统计ID
     *
     * @return 统计ID
     */
    private static String generateId() {
        return "HOME_STATS_" + System.currentTimeMillis();
    }
} 