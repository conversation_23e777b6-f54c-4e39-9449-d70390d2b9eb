package com.leliven.csc.domain.statistics.model.valueobject;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 趋势数据值对象
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class TrendData implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 日期
     */
    private LocalDate date;

    /**
     * 日期标签（格式化后的字符串）
     */
    private String dateLabel;

    /**
     * 生产工单数量
     */
    private Integer productionWorkOrders;

    /**
     * 处理工单数量
     */
    private Integer processedWorkOrders;

    public static TrendData of(LocalDate date, String dateLabel, Integer productionWorkOrders, Integer processedWorkOrders) {
        return new TrendData(date, dateLabel, productionWorkOrders, processedWorkOrders);
    }

    /**
     * 创建空的趋势数据
     *
     * @param date 日期
     * @param dateLabel 日期标签
     * @return 空的趋势数据
     */
    public static TrendData empty(LocalDate date, String dateLabel) {
        return new TrendData(date, dateLabel, 0, 0);
    }

    /**
     * 设置生产工单数量
     *
     * @param count 数量
     * @return 当前对象
     */
    public TrendData withProductionWorkOrders(Integer count) {
        this.productionWorkOrders = count;
        return this;
    }

    /**
     * 设置处理工单数量
     *
     * @param count 数量
     * @return 当前对象
     */
    public TrendData withProcessedWorkOrders(Integer count) {
        this.processedWorkOrders = count;
        return this;
    }

    /**
     * 趋势数据集合
     */
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendSeries implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 系列名称
         */
        private String name;

        /**
         * 趋势数据列表
         */
        private List<TrendData> data;

        public static TrendSeries of(String name, List<TrendData> data) {
            return new TrendSeries(name, data);
        }
    }
} 