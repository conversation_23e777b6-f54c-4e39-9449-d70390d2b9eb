package com.leliven.csc.domain.statistics.service.impl;

import com.leliven.csc.domain.statistics.model.HomeStatistics;
import com.leliven.csc.domain.statistics.model.valueobject.*;
import com.leliven.csc.domain.statistics.repository.HomeStatisticsRepository;
import com.leliven.csc.domain.statistics.service.HomeStatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * 首页统计领域服务实现
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HomeStatisticsServiceImpl implements HomeStatisticsService {

    private final HomeStatisticsRepository homeStatisticsRepository;

    @Override
    public HomeStatistics generateHomeStatistics(String tenantId, List<Long> parkLotIds, LocalDate startDate, LocalDate endDate) {
        log.info("生成首页统计数据，租户: {}, 车场: {}, 时间范围: {} - {}", tenantId, parkLotIds, startDate, endDate);

        // 创建首页统计聚合根
        HomeStatistics homeStatistics = HomeStatistics.create(tenantId, parkLotIds);

        // 获取客诉问题类型统计
        List<ComplaintType> complaintTypes = homeStatisticsRepository.getComplaintTypeStatistics(
                tenantId, parkLotIds, startDate, endDate);
        homeStatistics.withComplaintTypes(complaintTypes);

        // 获取客诉来源渠道分布
        List<ComplaintSource> complaintSources = homeStatisticsRepository.getComplaintSourceStatistics(
                tenantId, parkLotIds, startDate, endDate);
        homeStatistics.withComplaintSources(complaintSources);

        // 获取工单分析数据
        WorkOrderAnalysis workOrderAnalysis = homeStatisticsRepository.getWorkOrderAnalysis(
                tenantId, parkLotIds, startDate, endDate);
        homeStatistics.withWorkOrderAnalysis(workOrderAnalysis);

        // 获取工单处理趋势数据
        List<TrendData> trendData = homeStatisticsRepository.getWorkOrderTrendData(
                tenantId, parkLotIds, startDate, endDate);
        homeStatistics.withWorkOrderTrend(trendData);

        // 获取异常订单分析数据
        List<ComplaintType> abnormalOrderAnalysis = homeStatisticsRepository.getAbnormalOrderAnalysis(
                tenantId, parkLotIds, startDate, endDate);
        homeStatistics.withAbnormalOrderAnalysis(abnormalOrderAnalysis);

        // 计算占比
        homeStatistics.calculateComplaintTypePercentages();
        homeStatistics.calculateComplaintSourcePercentages();

        log.info("首页统计数据生成完成，统计ID: {}", homeStatistics.getId());
        return homeStatistics;
    }

    @Override
    public HomeStatistics getRealtimeHomeStatistics(String tenantId, List<Long> parkLotIds) {
        log.info("获取实时首页统计数据，租户: {}, 车场: {}", tenantId, parkLotIds);

        // 使用当天作为时间范围
        LocalDate today = LocalDate.now();
        return generateHomeStatistics(tenantId, parkLotIds, today, today);
    }

    @Override
    public HomeStatistics refreshStatistics(HomeStatistics homeStatistics) {
        log.info("刷新统计数据，统计ID: {}", homeStatistics.getId());

        // 重新生成统计数据
        LocalDate startDate = LocalDate.now().minusDays(7);
        LocalDate endDate = LocalDate.now();

        return generateHomeStatistics(
                homeStatistics.getTenantId(),
                homeStatistics.getParkLotIds(),
                startDate,
                endDate
        );
    }
} 