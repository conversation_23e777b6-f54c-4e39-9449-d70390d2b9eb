package com.leliven.csc.domain.work.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.leliven.csc.domain.work.exception.WorkOrderAlreadyClosedException;
import com.leliven.csc.domain.work.model.valueobject.*;
import com.leliven.plugin.domain.base.TenantDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.utils.ObjectValidator;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 工单领域对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkOrder extends TenantDomain {

	/**
	 * 工单编号
	 */
	private String code;
	/**
	 * 工单来源
	 * <p>
	 * {@link WorkOrderSource}
	 */
	private WorkOrderSource source;
	/**
	 * 工单级别，1-普通，2-重要，3-紧急，4-非常紧急
	 */
	private Integer level;
	/**
	 * 工单类别
	 * <p>
	 * {@link WorkOrderCategory}
	 */
	private String category;
	/**
	 * 工单类型
	 */
	private String type;
	/**
	 * 标题
	 */
	private String title;
	/**
	 * 工单描述
	 */
	private String description;
	/**
	 * 备注
	 */
	private String remarks;

	/**
	 * 附件地址
	 */
	private List<String> attachmentAddresses;
	/**
	 * 附件集合
	 */
	private WorkOrderAttachments attachments;
	/**
	 * 车场ID
	 */
	private Long parkLotId;

	/**
	 * 车场名称
	 */
	private String parkLotName;

	/**
	 * 车位ID
	 */
	private Long placeId;

	/**
	 * 车位编号
	 */
	private String placeCode;

	/**
	 * 设备类型
	 */
	private Integer deviceType;

	/**
	 * 设备地址
	 */
	private String deviceAddress;

	/**
	 * 处理方式
	 */
	private String processMethod;

	/**
	 * 处理结果
	 */
	private String result;

	/**
	 * 处理结束时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date handleEndTime;
	/**
	 * 是否已读
	 * <p>
	 * 未读-0 已读-1
	 */
	private Integer opRead;
	/**
	 * 告警ID
	 */
	private Long alarmId;
	/**
	 * 业务代码
	 */
	private String bizCode;
	/**
	 * 工单配置id
	 * <p>
	 * {@link WorkOrderConfig}
	 */
	private Long configId;
	/**
	 * 总耗时 单位：秒
	 */
	private Long totalProcessingTime;
	/**
	 * 工单关闭模式
	 * <p>
	 * {@link WorkOrderOperationMode}
	 */
	private WorkOrderOperationMode completeMode;
	/**
	 * 预响应时间
	 */
	private Date preRespTime;
	/**
	 * 预完成时间
	 */
	private Date preCompTime;
	/**
	 * 扩展参数
	 */
	private Map<String, String> extentParams;
	/**
	 * 阅读者
	 */
	private Set<Reader> readers;
	/**
	 * 发起人
	 */
	private Sponsor sponsor;
	/**
	 * 当前处理人
	 */
	private Set<Processor> processors;
	/**
	 * 工单处理流程列表
	 */
	private WorkOrderProcessManager workOrderProcessManager;


	public WorkOrder() {
		this(null);
	}

	public WorkOrder(Long id) {
		this.id = id;
		this.readers = new HashSet<>();
		this.processors = new HashSet<>();
		this.attachmentAddresses = new ArrayList<>();
		this.workOrderProcessManager = new WorkOrderProcessManager(id);
	}

	/**
	 * 创建工单
	 *
	 * @param code 工单编号
	 * @return this
	 */
	public WorkOrder create(String code, WorkOrderConfig config) {
		this.code = code;
		this.opRead = 0;
		this.status = WorkOrderStatus.HANDLE.getKey();
		this.createTime = new Date();
		this.title = config.getDisplay();
		this.category = config.getCategory();
		this.preRespTime = config.calculatePreResponseTime(this.createTime);
		this.preCompTime = config.calculatePreCompleteTime(this.createTime);
		this.setIfLevelNotSet(config::getLevel);
		this.setIfDescriptionNotSet(() -> config.parseContentTemplate(convert2TemplateParamMap()));
		this.workOrderProcessManager.belongToTenant(this.tenantId)
			.startProcess(new Processor(this.sponsor), this.source.getMode());
		return this;
	}

	/**
	 * 指派工单
	 *
	 * <p>暂时自动指派</p>
	 */
	public void assign() {
		this.workOrderProcessManager.createNextProcess(WorkOrderProcessOperation.ASSIGN, p -> p
			.addCurrentProcessor(new Processor(this.sponsor))
			.addNextProcessors(this.processors)
		);
	}

	/**
	 * 指派工单
	 */
	public void assign(WorkOrderProcess assignProcess) {
		this.resetProcessors(assignProcess.getNextProcessors());
		this.workOrderProcessManager.createNextProcess(() -> assignProcess.operation(WorkOrderProcessOperation.ASSIGN));
	}

	/**
	 * 转交工单
	 *
	 * @param transferProcess 转交流程 {@link WorkOrderProcess}
	 */
	public void transfer(WorkOrderProcess transferProcess) {
		this.resetProcessors(transferProcess.getNextProcessors());
		this.workOrderProcessManager.createNextProcess(() -> transferProcess.operation(WorkOrderProcessOperation.TRANSFER));
	}

	/**
	 * 评论工单
	 *
	 * @param process 评论流程
	 */
	public void comment(WorkOrderProcess process) {
		this.workOrderProcessManager.createNextProcess(() -> process.operation(WorkOrderProcessOperation.COMMENT));

	}

	/**
	 * 关闭工单
	 *
	 * @param process 处理流程
	 * @param type   关闭类型
	 * @return this
	 */
	public WorkOrder close(WorkOrderProcess process, String type) {
		if (Func.isNotBlank(type)) {
			this.type = type;
		}
		this.opRead = 1;
		this.status = WorkOrderStatus.CLOSE.getKey();
		this.processMethod = process.getProcessMethod();
		this.result = process.getProcessDesc();
		this.handleEndTime = process.getCreateTime();
		this.completeMode = process.getOperationMode();
		this.calculateTotalProcessingTime();
		this.resetProcessors(process.getCurrentProcessors());
		this.workOrderProcessManager.closeProcess(process);
		return this;
	}

	/**
	 * 初始化工单处理流程管理器
	 *
	 * @param processes  工单处理流程
	 * @param processors 处理人
	 */
	public void initProcessManager(List<WorkOrderProcess> processes, List<WorkOrderProcessor> processors) {
		this.workOrderProcessManager.belongTo(id)
			.belongToTenant(this.tenantId)
			.init(processes, processors);
	}

	/**
	 * 重置当前处理人
	 *
	 * @param newProcessors 新的当前处理人
	 */
	public void resetProcessors(Set<Processor> newProcessors) {
		this.processors.clear();
		this.processors.addAll(newProcessors);
	}

	/**
	 * 添加发起人
	 *
	 * @param sponsor 发起人
	 * @return this
	 */
	public WorkOrder addSponsor(Sponsor sponsor) {
		this.sponsor = sponsor;
		return this;
	}

	/**
	 * 添加阅读者
	 *
	 * @param reader 阅读者
	 * @return this
	 */
	public WorkOrder addReader(Reader reader) {
		this.readers.add(reader);
		return this;
	}

	/**
	 * 添加处理人
	 *
	 * @param processor 处理人
	 * @return this
	 */
	public WorkOrder addProcessor(Processor processor) {
		this.processors.add(processor);
		return this;
	}

	/**
	 * 添加处理人
	 *
	 * @param processors 处理人
	 * @return this
	 */
	public WorkOrder addProcessors(List<Processor> processors) {
		this.processors.addAll(processors);
		return this;
	}

	/**
	 * 添加一堆附件地址
	 *
	 * @param attachmentAddresses 一堆附件地址
	 * @return this
	 */
	public WorkOrder addAttachmentAddresses(List<String> attachmentAddresses) {
		if (Func.isNotEmpty(attachmentAddresses)) {
			this.attachmentAddresses.addAll(attachmentAddresses);
		}
		return this;
	}

	/**
	 * 判断当处理人列表只有一个人时，
	 * 是否是同一个发起人和处理人
	 *
	 * @return true: 是 false: 否
	 */
	@JsonIgnore
	public boolean isSameSponsorWhenOnlyOneProcessor() {
		return this.processors.size() == 1 && isProcessorsContainsSponsor();
	}

	/**
	 * 是否处理人列表包含发起人
	 *
	 * @return true: 是 false: 否
	 */
	@JsonIgnore
	public boolean isProcessorsContainsSponsor() {
		return this.sponsor.getId().equals(this.processors.iterator().next().getId());
	}

	/**
	 * 判断工单是否关闭
	 *
	 * @return true: 是 false: 否
	 */
	public boolean isClosed() {
		return WorkOrderStatus.CLOSE.getKey().equals(this.status);
	}

	/**
	 * 计算并设置总耗时（秒）
	 * <p>
	 * 总耗时 = 处理结束时间 - 创建时间
	 */
	public void calculateTotalProcessingTime() {
		if (handleEndTime != null) {
			this.totalProcessingTime = DateUtil.between(getCreateTime(), handleEndTime).getSeconds();
		}
	}

	/**
	 * 判断是否未指定处理人
	 *
	 * @return true: 是 false: 否
	 */
	public boolean isProcessorUnspecified() {
		return Func.isEmpty(this.processors);
	}

	/**
	 * 获取所有处理人
	 *
	 * @return 所有处理人
	 */
	public List<WorkOrderProcessor> allProcessor() {
		return new ArrayList<>(this.workOrderProcessManager.getProcessors().values());
	}

	/**
	 * 获取当前处理流程
	 *
	 * @return 当前处理流程
	 */
	public WorkOrderProcess currentProcess() {
		return this.workOrderProcessManager.lastProcess();
	}

	/**
	 * 获取当前处理人姓名
	 *
	 * @return 当前处理人姓名
	 */
	public List<String> currentProcessorName() {
		return this.processors.stream().map(Person::getName).collect(Collectors.toList());
	}

	/**
	 * 验证工单是否关闭
	 * <p>
	 * 如果已关闭，则抛出异常
	 *
	 * @throws WorkOrderAlreadyClosedException 工单已关闭异常
	 */
	public void verifyWorkOrderShouldUnclosed() {
		ObjectValidator.requireFalse(isClosed(), WorkOrderAlreadyClosedException::new);
	}

	/**
	 * 验证处理人是否不为空
	 * <p>
	 * 如果为空，则抛出异常
	 *
	 * @throws org.springblade.core.log.exception.ServiceException 业务异常
	 */
	public void verifyProcessorsShouldNotEmpty() {
		ObjectValidator.requireNotEmpty(processors, "处理人必选");
	}

	/**
	 * 如果来源是手动，则验证处理人是否不为空
	 * <p>
	 * 如果为空，则抛出异常
	 *
	 * @throws org.springblade.core.log.exception.ServiceException 业务异常
	 */
	public void verifyProcessorsRequireIfNecessary() {
		if (this.source.isManual()) {
			verifyProcessorsShouldNotEmpty();
		}
	}

	/**
	 * 验证发起人是否不为空
	 * <p>
	 * 如果为空，则抛出异常
	 *
	 * @throws org.springblade.core.log.exception.ServiceException 业务异常
	 */
	public void verifySponsorShouldNotNull() {
		ObjectValidator.requireTrue(Objects.nonNull(sponsor) && Objects.nonNull(sponsor.getId()),
			"发起人必填");
	}

	/**
	 * 验证描述是否不为空
	 * <p>
	 * 如果为空，则抛出异常
	 *
	 * @throws org.springblade.core.log.exception.ServiceException 业务异常
	 */
	public void verifyDescriptionShouldNotBlank() {
		ObjectValidator.requireNotBlank(description, "工单描述必填");
	}

	/**
	 * 验证来源是否不为空
	 * <p>
	 * 如果为空，则抛出异常
	 *
	 * @throws org.springblade.core.log.exception.ServiceException 业务异常
	 */
	public void verifySourceShouldNotNull() {
		ObjectValidator.requireNonNull(source, "工单来源不能为空");
		ObjectValidator.requireNonNull(source.getChannel(), "工单来源渠道不能为空");
		ObjectValidator.requireNonNull(source.getMode(), "工单来源操作模式不能为空");
	}

	/**
	 * 转换为模板参数
	 *
	 * @return 模板参数
	 */
	private Map<String, Object> convert2TemplateParamMap() {
		Map<String, Object> map = new HashMap<>();
		map.put("parklotName", this.parkLotName);
		map.put("placeName", this.placeCode);
		map.put("placeCode", this.placeCode);
		map.putAll(this.extentParams);
		return map;
	}

	/**
	 * 如果级别为空，则设置级别
	 *
	 * @param levelSupplier 级别提供者
	 */
	private void setIfLevelNotSet(Supplier<Integer> levelSupplier) {
		ObjectValidator.requireNonNull(levelSupplier);

		if (Objects.isNull(this.level)) {
			this.level = levelSupplier.get();
		}
	}

	/**
	 * 如果描述为空，则设置描述
	 *
	 * @param descriptionSupplier 描述提供者
	 */
	@JsonIgnore
	private void setIfDescriptionNotSet(Supplier<String> descriptionSupplier) {
		ObjectValidator.requireNonNull(descriptionSupplier);

		if (Func.isBlank(this.description)) {
			this.description = descriptionSupplier.get();
		}
	}

	public WorkOrder addAttachments(WorkOrderAttachments attachments) {
		this.attachments = attachments;
		return this;
	}
}

