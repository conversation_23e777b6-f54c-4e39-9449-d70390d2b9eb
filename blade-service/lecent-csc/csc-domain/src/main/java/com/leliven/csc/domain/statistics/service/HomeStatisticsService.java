package com.leliven.csc.domain.statistics.service;

import com.leliven.csc.domain.statistics.model.HomeStatistics;

import java.time.LocalDate;
import java.util.List;

/**
 * 首页统计领域服务接口
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public interface HomeStatisticsService {

    /**
     * 生成首页统计数据
     *
     * @param tenantId 租户ID
     * @param parkLotIds 车场ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 首页统计数据
     */
    HomeStatistics generateHomeStatistics(String tenantId, List<Long> parkLotIds, LocalDate startDate, LocalDate endDate);

    /**
     * 获取实时首页统计数据
     *
     * @param tenantId 租户ID
     * @param parkLotIds 车场ID列表
     * @return 首页统计数据
     */
    HomeStatistics getRealtimeHomeStatistics(String tenantId, List<Long> parkLotIds);

    /**
     * 刷新统计数据
     *
     * @param homeStatistics 首页统计数据
     * @return 刷新后的统计数据
     */
    HomeStatistics refreshStatistics(HomeStatistics homeStatistics);
} 