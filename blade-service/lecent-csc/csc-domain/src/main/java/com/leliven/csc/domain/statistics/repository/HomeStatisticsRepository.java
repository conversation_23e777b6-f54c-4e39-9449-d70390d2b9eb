package com.leliven.csc.domain.statistics.repository;

import com.leliven.csc.domain.statistics.model.valueobject.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 首页统计仓储接口
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public interface HomeStatisticsRepository {

    /**
     * 获取客诉问题类型统计
     *
     * @param tenantId 租户ID
     * @param parkLotIds 车场ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 客诉问题类型统计列表
     */
    List<ComplaintType> getComplaintTypeStatistics(String tenantId, List<Long> parkLotIds, LocalDate startDate, LocalDate endDate);

    /**
     * 获取客诉来源渠道分布
     *
     * @param tenantId 租户ID
     * @param parkLotIds 车场ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 客诉来源分布列表
     */
    List<ComplaintSource> getComplaintSourceStatistics(String tenantId, List<Long> parkLotIds, LocalDate startDate, LocalDate endDate);

    /**
     * 获取工单分析数据
     *
     * @param tenantId 租户ID
     * @param parkLotIds 车场ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 工单分析数据
     */
    WorkOrderAnalysis getWorkOrderAnalysis(String tenantId, List<Long> parkLotIds, LocalDate startDate, LocalDate endDate);

    /**
     * 获取工单处理趋势数据
     *
     * @param tenantId 租户ID
     * @param parkLotIds 车场ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 工单趋势数据列表
     */
    List<TrendData> getWorkOrderTrendData(String tenantId, List<Long> parkLotIds, LocalDate startDate, LocalDate endDate);

    /**
     * 获取异常订单分析数据
     *
     * @param tenantId 租户ID
     * @param parkLotIds 车场ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 异常订单分析列表
     */
    List<ComplaintType> getAbnormalOrderAnalysis(String tenantId, List<Long> parkLotIds, LocalDate startDate, LocalDate endDate);
} 